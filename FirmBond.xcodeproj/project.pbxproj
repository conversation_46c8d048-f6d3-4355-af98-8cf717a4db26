// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 77;
	objects = {

/* Begin PBXContainerItemProxy section */
		6510E0152DEDFF2300331A68 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 6510DFF82DEDFF2200331A68 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 6510DFFF2DEDFF2200331A68;
			remoteInfo = FirmBond;
		};
		6510E01F2DEDFF2300331A68 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 6510DFF82DEDFF2200331A68 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 6510DFFF2DEDFF2200331A68;
			remoteInfo = FirmBond;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXFileReference section */
		6510E0002DEDFF2200331A68 /* FirmBond.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = FirmBond.app; sourceTree = BUILT_PRODUCTS_DIR; };
		6510E0142DEDFF2300331A68 /* FirmBondTests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = FirmBondTests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		6510E01E2DEDFF2300331A68 /* FirmBondUITests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = FirmBondUITests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
/* End PBXFileReference section */

/* Begin PBXFileSystemSynchronizedBuildFileExceptionSet section */
		6510E0262DEDFF2300331A68 /* Exceptions for "FirmBond" folder in "FirmBond" target */ = {
			isa = PBXFileSystemSynchronizedBuildFileExceptionSet;
			membershipExceptions = (
				Info.plist,
			);
			target = 6510DFFF2DEDFF2200331A68 /* FirmBond */;
		};
/* End PBXFileSystemSynchronizedBuildFileExceptionSet section */

/* Begin PBXFileSystemSynchronizedRootGroup section */
		6510E0022DEDFF2200331A68 /* FirmBond */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			exceptions = (
				6510E0262DEDFF2300331A68 /* Exceptions for "FirmBond" folder in "FirmBond" target */,
			);
			path = FirmBond;
			sourceTree = "<group>";
		};
		6510E0172DEDFF2300331A68 /* FirmBondTests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = FirmBondTests;
			sourceTree = "<group>";
		};
		6510E0212DEDFF2300331A68 /* FirmBondUITests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = FirmBondUITests;
			sourceTree = "<group>";
		};
/* End PBXFileSystemSynchronizedRootGroup section */

/* Begin PBXFrameworksBuildPhase section */
		6510DFFD2DEDFF2200331A68 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		6510E0112DEDFF2300331A68 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		6510E01B2DEDFF2300331A68 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		6510DFF72DEDFF2200331A68 = {
			isa = PBXGroup;
			children = (
				6510E0022DEDFF2200331A68 /* FirmBond */,
				6510E0172DEDFF2300331A68 /* FirmBondTests */,
				6510E0212DEDFF2300331A68 /* FirmBondUITests */,
				6510E0012DEDFF2200331A68 /* Products */,
			);
			sourceTree = "<group>";
		};
		6510E0012DEDFF2200331A68 /* Products */ = {
			isa = PBXGroup;
			children = (
				6510E0002DEDFF2200331A68 /* FirmBond.app */,
				6510E0142DEDFF2300331A68 /* FirmBondTests.xctest */,
				6510E01E2DEDFF2300331A68 /* FirmBondUITests.xctest */,
			);
			name = Products;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		6510DFFF2DEDFF2200331A68 /* FirmBond */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 6510E0272DEDFF2300331A68 /* Build configuration list for PBXNativeTarget "FirmBond" */;
			buildPhases = (
				6510DFFC2DEDFF2200331A68 /* Sources */,
				6510DFFD2DEDFF2200331A68 /* Frameworks */,
				6510DFFE2DEDFF2200331A68 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			fileSystemSynchronizedGroups = (
				6510E0022DEDFF2200331A68 /* FirmBond */,
			);
			name = FirmBond;
			packageProductDependencies = (
			);
			productName = FirmBond;
			productReference = 6510E0002DEDFF2200331A68 /* FirmBond.app */;
			productType = "com.apple.product-type.application";
		};
		6510E0132DEDFF2300331A68 /* FirmBondTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 6510E02C2DEDFF2300331A68 /* Build configuration list for PBXNativeTarget "FirmBondTests" */;
			buildPhases = (
				6510E0102DEDFF2300331A68 /* Sources */,
				6510E0112DEDFF2300331A68 /* Frameworks */,
				6510E0122DEDFF2300331A68 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				6510E0162DEDFF2300331A68 /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				6510E0172DEDFF2300331A68 /* FirmBondTests */,
			);
			name = FirmBondTests;
			packageProductDependencies = (
			);
			productName = FirmBondTests;
			productReference = 6510E0142DEDFF2300331A68 /* FirmBondTests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
		6510E01D2DEDFF2300331A68 /* FirmBondUITests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 6510E02F2DEDFF2300331A68 /* Build configuration list for PBXNativeTarget "FirmBondUITests" */;
			buildPhases = (
				6510E01A2DEDFF2300331A68 /* Sources */,
				6510E01B2DEDFF2300331A68 /* Frameworks */,
				6510E01C2DEDFF2300331A68 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				6510E0202DEDFF2300331A68 /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				6510E0212DEDFF2300331A68 /* FirmBondUITests */,
			);
			name = FirmBondUITests;
			packageProductDependencies = (
			);
			productName = FirmBondUITests;
			productReference = 6510E01E2DEDFF2300331A68 /* FirmBondUITests.xctest */;
			productType = "com.apple.product-type.bundle.ui-testing";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		6510DFF82DEDFF2200331A68 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1630;
				LastUpgradeCheck = 1630;
				TargetAttributes = {
					6510DFFF2DEDFF2200331A68 = {
						CreatedOnToolsVersion = 16.3;
					};
					6510E0132DEDFF2300331A68 = {
						CreatedOnToolsVersion = 16.3;
						TestTargetID = 6510DFFF2DEDFF2200331A68;
					};
					6510E01D2DEDFF2300331A68 = {
						CreatedOnToolsVersion = 16.3;
						TestTargetID = 6510DFFF2DEDFF2200331A68;
					};
				};
			};
			buildConfigurationList = 6510DFFB2DEDFF2200331A68 /* Build configuration list for PBXProject "FirmBond" */;
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 6510DFF72DEDFF2200331A68;
			minimizedProjectReferenceProxies = 1;
			preferredProjectObjectVersion = 77;
			productRefGroup = 6510E0012DEDFF2200331A68 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				6510DFFF2DEDFF2200331A68 /* FirmBond */,
				6510E0132DEDFF2300331A68 /* FirmBondTests */,
				6510E01D2DEDFF2300331A68 /* FirmBondUITests */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		6510DFFE2DEDFF2200331A68 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		6510E0122DEDFF2300331A68 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		6510E01C2DEDFF2300331A68 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		6510DFFC2DEDFF2200331A68 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		6510E0102DEDFF2300331A68 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		6510E01A2DEDFF2300331A68 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		6510E0162DEDFF2300331A68 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 6510DFFF2DEDFF2200331A68 /* FirmBond */;
			targetProxy = 6510E0152DEDFF2300331A68 /* PBXContainerItemProxy */;
		};
		6510E0202DEDFF2300331A68 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 6510DFFF2DEDFF2200331A68 /* FirmBond */;
			targetProxy = 6510E01F2DEDFF2300331A68 /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		6510E0282DEDFF2300331A68 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = FirmBond/FirmBond.entitlements;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 64NMP2XKXR;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = FirmBond/Info.plist;
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				IPHONEOS_DEPLOYMENT_TARGET = 17.6;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = site.firmbond.FirmBond;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		6510E0292DEDFF2300331A68 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = FirmBond/FirmBond.entitlements;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 64NMP2XKXR;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = FirmBond/Info.plist;
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				IPHONEOS_DEPLOYMENT_TARGET = 17.6;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = site.firmbond.FirmBond;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
		6510E02A2DEDFF2300331A68 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				DEVELOPMENT_TEAM = 64NMP2XKXR;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.4;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		6510E02B2DEDFF2300331A68 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				DEVELOPMENT_TEAM = 64NMP2XKXR;
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.4;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		6510E02D2DEDFF2300331A68 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 64NMP2XKXR;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 17.6;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = site.firmbond.FirmBondTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/FirmBond.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/FirmBond";
			};
			name = Debug;
		};
		6510E02E2DEDFF2300331A68 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 64NMP2XKXR;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 17.6;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = site.firmbond.FirmBondTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/FirmBond.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/FirmBond";
			};
			name = Release;
		};
		6510E0302DEDFF2300331A68 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 64NMP2XKXR;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 17.6;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = site.firmbond.FirmBondUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_TARGET_NAME = FirmBond;
			};
			name = Debug;
		};
		6510E0312DEDFF2300331A68 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 64NMP2XKXR;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 17.6;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = site.firmbond.FirmBondUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_TARGET_NAME = FirmBond;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		6510DFFB2DEDFF2200331A68 /* Build configuration list for PBXProject "FirmBond" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				6510E02A2DEDFF2300331A68 /* Debug */,
				6510E02B2DEDFF2300331A68 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		6510E0272DEDFF2300331A68 /* Build configuration list for PBXNativeTarget "FirmBond" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				6510E0282DEDFF2300331A68 /* Debug */,
				6510E0292DEDFF2300331A68 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		6510E02C2DEDFF2300331A68 /* Build configuration list for PBXNativeTarget "FirmBondTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				6510E02D2DEDFF2300331A68 /* Debug */,
				6510E02E2DEDFF2300331A68 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		6510E02F2DEDFF2300331A68 /* Build configuration list for PBXNativeTarget "FirmBondUITests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				6510E0302DEDFF2300331A68 /* Debug */,
				6510E0312DEDFF2300331A68 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 6510DFF82DEDFF2200331A68 /* Project object */;
}
