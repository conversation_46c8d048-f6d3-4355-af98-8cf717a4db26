//
//  ContactDetailView.swift
//  FirmBond
//
//  Created by <PERSON><PERSON> on 02/06/2025.
//

import SwiftUI
import MessageUI

struct ContactDetailView: View {
    
    // MARK: - Properties
    let person: Person
    @ObservedObject var viewModel: ContactsViewModel
    @Environment(\.dismiss) private var dismiss
    
    @State private var isShowingEditView = false
    @State private var showingDeleteConfirmation = false
    @State private var isShowingMessageComposer = false
    @State private var isShowingMailComposer = false
    
    // MARK: - Body
    var body: some View {
        NavigationView {
            ZStack {
                // Background
                backgroundGradient
                
                // Content
                ScrollView {
                    VStack(spacing: DesignSystem.Spacing.xl) {
                        // Header with photo and name
                        headerSection
                        
                        // Contact actions
                        contactActionsSection
                        
                        // Information sections
                        informationSections
                        
                        // Notes section
                        if let notes = person.notes, !notes.isEmpty {
                            notesSection
                        }
                        
                        // Metadata
                        metadataSection
                    }
                    .padding(DesignSystem.Spacing.lg)
                }
            }
            .navigationTitle("")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Done") {
                        dismiss()
                    }
                    .foregroundColor(DesignSystem.Colors.subtleText)
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Menu {
                        Button("Edit Contact") {
                            isShowingEditView = true
                        }
                        
                        Divider()
                        
                        Button("Delete Contact", role: .destructive) {
                            showingDeleteConfirmation = true
                        }
                    } label: {
                        Image(systemName: "ellipsis.circle")
                            .foregroundColor(DesignSystem.Colors.subtleText)
                    }
                }
            }
        }
        .sheet(isPresented: $isShowingEditView) {
            // EditContactView will be created separately
            Text("Edit functionality coming soon")
        }
        .confirmationDialog(
            "Delete Contact",
            isPresented: $showingDeleteConfirmation,
            titleVisibility: .visible
        ) {
            Button("Delete", role: .destructive) {
                viewModel.deletePerson(person)
                dismiss()
            }
            Button("Cancel", role: .cancel) {}
        } message: {
            Text("Are you sure you want to delete \(person.fullName)? This action cannot be undone.")
        }
    }
    
    // MARK: - Private Views
    private var backgroundGradient: some View {
        LinearGradient(
            colors: [
                DesignSystem.Colors.primaryCream,
                DesignSystem.Colors.secondaryCream
            ],
            startPoint: .topLeading,
            endPoint: .bottomTrailing
        )
        .ignoresSafeArea()
    }
    
    private var headerSection: some View {
        VStack(spacing: DesignSystem.Spacing.lg) {
            // Profile photo
            Group {
                if let photoData = person.photoData,
                   let uiImage = UIImage(data: photoData) {
                    Image(uiImage: uiImage)
                        .resizable()
                        .aspectRatio(contentMode: .fill)
                        .frame(width: 140, height: 140)
                        .clipShape(Circle())
                } else {
                    Circle()
                        .fill(
                            LinearGradient(
                                colors: [
                                    DesignSystem.Colors.mutedGold,
                                    DesignSystem.Colors.sageGreen
                                ],
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            )
                        )
                        .frame(width: 140, height: 140)
                        .overlay(
                            Text(person.initials)
                                .font(.system(size: 48, weight: .semibold))
                                .foregroundColor(.white)
                        )
                }
            }
            .shadow(
                color: DesignSystem.Colors.glassShadow,
                radius: 12,
                x: 0,
                y: 6
            )
            
            // Name and relationship
            VStack(spacing: DesignSystem.Spacing.xs) {
                Text(person.fullName)
                    .font(DesignSystem.Typography.largeTitle)
                    .fontWeight(.bold)
                    .foregroundColor(DesignSystem.Colors.warmBlack)
                    .multilineTextAlignment(.center)
                
                if let relationship = person.relationship, !relationship.isEmpty {
                    Text(relationship)
                        .font(DesignSystem.Typography.title3)
                        .foregroundColor(DesignSystem.Colors.subtleText)
                        .multilineTextAlignment(.center)
                }
            }
        }
    }
    
    private var contactActionsSection: some View {
        HStack(spacing: DesignSystem.Spacing.md) {
            // Call button
            if let phoneNumber = person.phoneNumber, !phoneNumber.isEmpty {
                ContactActionButton(
                    icon: "phone.fill",
                    title: "Call",
                    color: DesignSystem.Colors.sageGreen
                ) {
                    if let url = URL(string: "tel://\(phoneNumber)") {
                        UIApplication.shared.open(url)
                    }
                }
            }
            
            // Message button
            if let phoneNumber = person.phoneNumber, !phoneNumber.isEmpty {
                ContactActionButton(
                    icon: "message.fill",
                    title: "Message",
                    color: DesignSystem.Colors.softBlue
                ) {
                    if let url = URL(string: "sms://\(phoneNumber)") {
                        UIApplication.shared.open(url)
                    }
                }
            }
            
            // Email button
            if let email = person.email, !email.isEmpty {
                ContactActionButton(
                    icon: "envelope.fill",
                    title: "Email",
                    color: DesignSystem.Colors.mutedGold
                ) {
                    if let url = URL(string: "mailto:\(email)") {
                        UIApplication.shared.open(url)
                    }
                }
            }
        }
    }
    
    private var informationSections: some View {
        VStack(spacing: DesignSystem.Spacing.lg) {
            // Phone number
            if let phoneNumber = person.phoneNumber, !phoneNumber.isEmpty {
                InfoSection(
                    title: "Phone",
                    value: person.displayPhoneNumber,
                    icon: "phone"
                )
            }
            
            // Email
            if let email = person.email, !email.isEmpty {
                InfoSection(
                    title: "Email",
                    value: email,
                    icon: "envelope"
                )
            }
            
            // Relationship
            if let relationship = person.relationship, !relationship.isEmpty {
                InfoSection(
                    title: "Relationship",
                    value: relationship,
                    icon: "person.2"
                )
            }
        }
    }
    
    private var notesSection: some View {
        GlassmorphicCard {
            VStack(alignment: .leading, spacing: DesignSystem.Spacing.sm) {
                HStack {
                    Image(systemName: "note.text")
                        .font(DesignSystem.Typography.callout)
                        .foregroundColor(DesignSystem.Colors.mutedGold)
                    
                    Text("Notes")
                        .font(DesignSystem.Typography.headline)
                        .foregroundColor(DesignSystem.Colors.warmBlack)
                    
                    Spacer()
                }
                
                Text(person.notes ?? "")
                    .font(DesignSystem.Typography.body)
                    .foregroundColor(DesignSystem.Colors.warmBlack)
                    .frame(maxWidth: .infinity, alignment: .leading)
            }
        }
    }
    
    private var metadataSection: some View {
        VStack(spacing: DesignSystem.Spacing.sm) {
            if let dateAdded = person.dateAdded {
                MetadataRow(
                    label: "Added",
                    value: DateFormatter.contactDate.string(from: dateAdded)
                )
            }
            
            if let lastContact = person.lastContactDate {
                MetadataRow(
                    label: "Last Contact",
                    value: DateFormatter.contactDate.string(from: lastContact)
                )
            }
        }
    }
}

// MARK: - Supporting Views
struct ContactActionButton: View {
    let icon: String
    let title: String
    let color: Color
    let action: () -> Void
    
    @State private var isPressed = false
    
    var body: some View {
        Button(action: action) {
            VStack(spacing: DesignSystem.Spacing.xs) {
                Circle()
                    .fill(color.opacity(0.2))
                    .frame(width: 56, height: 56)
                    .overlay(
                        Image(systemName: icon)
                            .font(.title2)
                            .foregroundColor(color)
                    )
                
                Text(title)
                    .font(DesignSystem.Typography.caption)
                    .foregroundColor(DesignSystem.Colors.subtleText)
            }
        }
        .buttonStyle(PlainButtonStyle())
        .scaleEffect(isPressed ? 0.95 : 1.0)
        .onLongPressGesture(minimumDuration: 0) { pressing in
            withAnimation(DesignSystem.Animations.easeInOut) {
                isPressed = pressing
            }
        } perform: {}
    }
}

struct InfoSection: View {
    let title: String
    let value: String
    let icon: String
    
    var body: some View {
        GlassmorphicCard {
            HStack(spacing: DesignSystem.Spacing.md) {
                Image(systemName: icon)
                    .font(DesignSystem.Typography.callout)
                    .foregroundColor(DesignSystem.Colors.mutedGold)
                    .frame(width: 24)
                
                VStack(alignment: .leading, spacing: 2) {
                    Text(title)
                        .font(DesignSystem.Typography.caption)
                        .foregroundColor(DesignSystem.Colors.subtleText)
                    
                    Text(value)
                        .font(DesignSystem.Typography.body)
                        .foregroundColor(DesignSystem.Colors.warmBlack)
                }
                
                Spacer()
            }
        }
    }
}

struct MetadataRow: View {
    let label: String
    let value: String
    
    var body: some View {
        HStack {
            Text(label)
                .font(DesignSystem.Typography.caption)
                .foregroundColor(DesignSystem.Colors.subtleText)
            
            Spacer()
            
            Text(value)
                .font(DesignSystem.Typography.monoFootnote)
                .foregroundColor(DesignSystem.Colors.subtleText)
        }
    }
}

// MARK: - Date Formatter Extension
extension DateFormatter {
    static let contactDate: DateFormatter = {
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.timeStyle = .none
        return formatter
    }()
}

// MARK: - Preview
#Preview {
    let samplePerson = Person(context: PersistenceController.preview.container.viewContext)
    samplePerson.firstName = "John"
    samplePerson.lastName = "Doe"
    samplePerson.email = "<EMAIL>"
    samplePerson.phoneNumber = "1234567890"
    samplePerson.relationship = "Friend"
    samplePerson.notes = "Met at the conference last year. Really interested in sustainable technology."
    samplePerson.dateAdded = Date()
    samplePerson.lastContactDate = Date()
    
    return ContactDetailView(
        person: samplePerson,
        viewModel: ContactsViewModel(viewContext: PersistenceController.preview.container.viewContext)
    )
}
