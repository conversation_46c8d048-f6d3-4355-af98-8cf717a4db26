//
//  PersonBubbleView.swift
//  FirmBond
//
//  Created by <PERSON><PERSON> on 02/06/2025.
//

import SwiftUI
import CoreData

struct PersonBubbleView: View {
    
    // MARK: - Properties
    let person: Person
    let isSelected: Bool
    let onTap: () -> Void
    let onDelete: (() -> Void)?
    
    @State private var showingDeleteConfirmation = false
    @State private var dragOffset = CGSize.zero
    @State private var isShowingActions = false
    @State private var showingContextMenu = false
    @State private var rightSwipeOffset: CGFloat = 0
    @State private var leftSwipeOffset: CGFloat = 0
    @State private var isLongPressing = false
    
    // MARK: - Initialization
    init(
        person: Person,
        isSelected: Bool = false,
        onTap: @escaping () -> Void,
        onDelete: (() -> Void)? = nil
    ) {
        self.person = person
        self.isSelected = isSelected
        self.onTap = onTap
        self.onDelete = onDelete
    }
    
    // MARK: - Body (Neuropsychology-Enhanced)
    var body: some View {
        ZStack {
            // Background swipe actions
            swipeActionsBackground

            // Main contact card
            ContactCard(isSelected: isSelected, onTap: {}) {
                HStack(spacing: DesignSystem.Spacing.md) {
                    // Profile image or initials
                    profileImageView

                    // Contact information
                    contactInfoView

                    Spacer()

                    // Status indicators
                    statusIndicators
                }
            }
            .offset(x: dragOffset.width)
            .scaleEffect(isLongPressing ? 0.98 : 1.0)
            .gesture(enhancedGestures)

            // Context menu overlay
            if showingContextMenu {
                contextMenuOverlay
                    .transition(.asymmetric(
                        insertion: .scale.combined(with: .opacity),
                        removal: .scale.combined(with: .opacity)
                    ))
            }
        }
        .confirmationDialog(
            "Delete Contact",
            isPresented: $showingDeleteConfirmation,
            titleVisibility: .visible
        ) {
            Button("Delete", role: .destructive) {
                withAnimation(DesignSystem.Animations.spring) {
                    onDelete?()
                }
            }
            Button("Cancel", role: .cancel) {}
        } message: {
            Text("Are you sure you want to delete \(person.fullName)?")
        }
    }
    
    // MARK: - Private Views
    private var profileImageView: some View {
        Group {
            if let photoData = person.photoData,
               let uiImage = UIImage(data: photoData) {
                Image(uiImage: uiImage)
                    .resizable()
                    .aspectRatio(contentMode: .fill)
                    .frame(width: 56, height: 56)
                    .clipShape(Circle())
            } else {
                Circle()
                    .fill(
                        LinearGradient(
                            colors: [
                                DesignSystem.Colors.mutedGold,
                                DesignSystem.Colors.sageGreen
                            ],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
                    .frame(width: 56, height: 56)
                    .overlay(
                        Text(person.initials)
                            .font(DesignSystem.Typography.title3)
                            .fontWeight(.semibold)
                            .foregroundColor(.white)
                    )
            }
        }
        .shadow(
            color: DesignSystem.Colors.glassShadow,
            radius: 4,
            x: 0,
            y: 2
        )
    }
    
    private var contactInfoView: some View {
        VStack(alignment: .leading, spacing: 4) {
            // Name
            Text(person.fullName)
                .font(DesignSystem.Typography.title3)
                .foregroundColor(DesignSystem.Colors.warmBlack)
                .lineLimit(1)
            
            // Relationship or email
            if let relationship = person.relationship, !relationship.isEmpty {
                Text(relationship)
                    .font(DesignSystem.Typography.callout)
                    .foregroundColor(DesignSystem.Colors.subtleText)
                    .lineLimit(1)
            } else if let email = person.email, !email.isEmpty {
                Text(email)
                    .font(DesignSystem.Typography.monoCallout)
                    .foregroundColor(DesignSystem.Colors.subtleText)
                    .lineLimit(1)
            }
            
            // Phone number
            if !person.displayPhoneNumber.isEmpty {
                Text(person.displayPhoneNumber)
                    .font(DesignSystem.Typography.monoFootnote)
                    .foregroundColor(DesignSystem.Colors.subtleText)
                    .lineLimit(1)
            }
        }
    }
    
    private var statusIndicators: some View {
        VStack(alignment: .trailing, spacing: 4) {
            // Recent contact indicator
            if let lastContact = person.lastContactDate {
                let daysSince = Calendar.current.dateComponents([.day], from: lastContact, to: Date()).day ?? 0
                
                if daysSince <= 7 {
                    Circle()
                        .fill(DesignSystem.Colors.sageGreen)
                        .frame(width: 8, height: 8)
                }
            }
            
            // Contact methods available
            HStack(spacing: 4) {
                if !(person.phoneNumber?.isEmpty ?? true) {
                    Image(systemName: "phone.fill")
                        .font(.caption2)
                        .foregroundColor(DesignSystem.Colors.subtleText)
                }
                
                if !(person.email?.isEmpty ?? true) {
                    Image(systemName: "envelope.fill")
                        .font(.caption2)
                        .foregroundColor(DesignSystem.Colors.subtleText)
                }
            }
        }
    }
    
    // MARK: - Neuropsychology-Enhanced Gesture Components

    // Swipe actions background (Progressive Disclosure)
    private var swipeActionsBackground: some View {
        HStack {
            // Right swipe action (primary contact method)
            if rightSwipeOffset > 50 {
                quickContactAction
                    .transition(.move(edge: .leading).combined(with: .opacity))
            }

            Spacer()

            // Left swipe action (delete)
            if leftSwipeOffset < -50 {
                deleteAction
                    .transition(.move(edge: .trailing).combined(with: .opacity))
            }
        }
    }

    private var quickContactAction: some View {
        RoundedRectangle(cornerRadius: DesignSystem.CornerRadius.large)
            .fill(DesignSystem.Colors.sageGreen.opacity(0.8))
            .frame(width: 80)
            .overlay(
                VStack(spacing: 4) {
                    Image(systemName: getPrimaryContactIcon())
                        .font(.title2)
                        .foregroundColor(.white)

                    Text(getPrimaryContactTitle())
                        .font(.caption2)
                        .fontWeight(.medium)
                        .foregroundColor(.white)
                }
            )
    }

    private var deleteAction: some View {
        RoundedRectangle(cornerRadius: DesignSystem.CornerRadius.large)
            .fill(Color.red.opacity(0.8))
            .frame(width: 80)
            .overlay(
                VStack(spacing: 4) {
                    Image(systemName: "trash")
                        .font(.title2)
                        .foregroundColor(.white)

                    Text("Delete")
                        .font(.caption2)
                        .fontWeight(.medium)
                        .foregroundColor(.white)
                }
            )
    }

    // Context menu overlay (Long Press)
    private var contextMenuOverlay: some View {
        VStack(spacing: DesignSystem.Spacing.sm) {
            // Contact preview
            contactPreview

            // Quick actions
            quickActionsMenu
        }
        .padding(DesignSystem.Spacing.lg)
        .background(
            RoundedRectangle(cornerRadius: DesignSystem.CornerRadius.large)
                .fill(.ultraThinMaterial)
                .shadow(
                    color: DesignSystem.Colors.glassShadow,
                    radius: 20,
                    x: 0,
                    y: 10
                )
        )
        .onTapGesture {
            withAnimation(.spring(response: 0.4, dampingFraction: 0.8)) {
                showingContextMenu = false
            }
        }
    }

    private var contactPreview: some View {
        HStack(spacing: DesignSystem.Spacing.md) {
            // Larger profile image
            Group {
                if let photoData = person.photoData,
                   let uiImage = UIImage(data: photoData) {
                    Image(uiImage: uiImage)
                        .resizable()
                        .aspectRatio(contentMode: .fill)
                        .frame(width: 80, height: 80)
                        .clipShape(Circle())
                } else {
                    Circle()
                        .fill(
                            LinearGradient(
                                colors: [
                                    DesignSystem.Colors.mutedGold,
                                    DesignSystem.Colors.sageGreen
                                ],
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            )
                        )
                        .frame(width: 80, height: 80)
                        .overlay(
                            Text(person.initials)
                                .font(.system(size: 28, weight: .semibold))
                                .foregroundColor(.white)
                        )
                }
            }

            VStack(alignment: .leading, spacing: 4) {
                Text(person.fullName)
                    .font(.system(size: 20, weight: .semibold))
                    .foregroundColor(DesignSystem.Colors.warmBlack)

                if let relationship = person.relationship, !relationship.isEmpty {
                    Text(relationship)
                        .font(.system(size: 16, weight: .medium))
                        .foregroundColor(DesignSystem.Colors.subtleText)
                }
            }

            Spacer()
        }
    }

    private var quickActionsMenu: some View {
        HStack(spacing: DesignSystem.Spacing.md) {
            // Call action
            if let phoneNumber = person.phoneNumber, !phoneNumber.isEmpty {
                ContextMenuButton(
                    icon: "phone.fill",
                    title: "Call",
                    color: DesignSystem.Colors.sageGreen
                ) {
                    if let url = URL(string: "tel://\(phoneNumber)") {
                        UIApplication.shared.open(url)
                    }
                    showingContextMenu = false
                }
            }

            // Message action
            if let phoneNumber = person.phoneNumber, !phoneNumber.isEmpty {
                ContextMenuButton(
                    icon: "message.fill",
                    title: "Message",
                    color: DesignSystem.Colors.softBlue
                ) {
                    if let url = URL(string: "sms://\(phoneNumber)") {
                        UIApplication.shared.open(url)
                    }
                    showingContextMenu = false
                }
            }

            // Email action
            if let email = person.email, !email.isEmpty {
                ContextMenuButton(
                    icon: "envelope.fill",
                    title: "Email",
                    color: DesignSystem.Colors.mutedGold
                ) {
                    if let url = URL(string: "mailto:\(email)") {
                        UIApplication.shared.open(url)
                    }
                    showingContextMenu = false
                }
            }

            // View details
            ContextMenuButton(
                icon: "person.circle",
                title: "Details",
                color: DesignSystem.Colors.warmBlack
            ) {
                onTap()
                showingContextMenu = false
            }
        }
    }
    
    // MARK: - Enhanced Gestures (Neuropsychology-Optimized)

    private var enhancedGestures: some Gesture {
        SimultaneousGesture(
            swipeGesture,
            longPressGesture
        )
    }

    private var swipeGesture: some Gesture {
        DragGesture()
            .onChanged { value in
                let translation = value.translation.width

                // Right swipe for quick action
                if translation > 0 {
                    rightSwipeOffset = translation
                    leftSwipeOffset = 0
                    dragOffset = CGSize(width: min(translation, 100), height: 0)
                }
                // Left swipe for delete
                else if translation < 0 && onDelete != nil {
                    leftSwipeOffset = translation
                    rightSwipeOffset = 0
                    dragOffset = CGSize(width: max(translation, -100), height: 0)
                }
            }
            .onEnded { value in
                let translation = value.translation.width

                withAnimation(.spring(response: 0.4, dampingFraction: 0.8)) {
                    // Right swipe action (quick contact)
                    if translation > 100 {
                        performPrimaryContactAction()
                    }
                    // Left swipe action (delete)
                    else if translation < -100 && onDelete != nil {
                        showingDeleteConfirmation = true
                    }

                    // Reset offsets
                    dragOffset = .zero
                    rightSwipeOffset = 0
                    leftSwipeOffset = 0
                }
            }
    }

    private var longPressGesture: some Gesture {
        LongPressGesture(minimumDuration: 0.5)
            .onChanged { pressing in
                withAnimation(.easeInOut(duration: 0.2)) {
                    isLongPressing = pressing
                }
            }
            .onEnded { _ in
                // Haptic feedback for long press
                let impactFeedback = UIImpactFeedbackGenerator(style: .medium)
                impactFeedback.impactOccurred()

                withAnimation(.spring(response: 0.4, dampingFraction: 0.8)) {
                    showingContextMenu = true
                    isLongPressing = false
                }
            }
    }

    // MARK: - Helper Methods

    private func getPrimaryContactIcon() -> String {
        if !(person.phoneNumber?.isEmpty ?? true) {
            return "phone.fill"
        } else if !(person.email?.isEmpty ?? true) {
            return "envelope.fill"
        }
        return "person.circle"
    }

    private func getPrimaryContactTitle() -> String {
        if !(person.phoneNumber?.isEmpty ?? true) {
            return "Call"
        } else if !(person.email?.isEmpty ?? true) {
            return "Email"
        }
        return "View"
    }

    private func performPrimaryContactAction() {
        // Haptic feedback
        let impactFeedback = UIImpactFeedbackGenerator(style: .medium)
        impactFeedback.impactOccurred()

        // Perform primary action
        if let phoneNumber = person.phoneNumber, !phoneNumber.isEmpty {
            if let url = URL(string: "tel://\(phoneNumber)") {
                UIApplication.shared.open(url)
            }
        } else if let email = person.email, !email.isEmpty {
            if let url = URL(string: "mailto:\(email)") {
                UIApplication.shared.open(url)
            }
        } else {
            onTap()
        }
    }
}

// MARK: - Context Menu Button Component
struct ContextMenuButton: View {
    let icon: String
    let title: String
    let color: Color
    let action: () -> Void

    @State private var isPressed = false

    var body: some View {
        Button(action: action) {
            VStack(spacing: 8) {
                Circle()
                    .fill(color.opacity(0.15))
                    .frame(width: 50, height: 50)
                    .overlay(
                        Image(systemName: icon)
                            .font(.title3)
                            .foregroundColor(color)
                    )

                Text(title)
                    .font(.caption)
                    .fontWeight(.medium)
                    .foregroundColor(DesignSystem.Colors.warmBlack)
            }
        }
        .buttonStyle(PlainButtonStyle())
        .scaleEffect(isPressed ? 0.9 : 1.0)
        .onLongPressGesture(minimumDuration: 0) { pressing in
            withAnimation(.easeInOut(duration: 0.1)) {
                isPressed = pressing
            }
        } perform: {}
    }
}

// MARK: - Compact Variant
struct PersonBubbleCompactView: View {
    let person: Person
    let onTap: () -> Void
    
    var body: some View {
        Button(action: onTap) {
            HStack(spacing: DesignSystem.Spacing.sm) {
                // Smaller profile image
                Group {
                    if let photoData = person.photoData,
                       let uiImage = UIImage(data: photoData) {
                        Image(uiImage: uiImage)
                            .resizable()
                            .aspectRatio(contentMode: .fill)
                            .frame(width: 40, height: 40)
                            .clipShape(Circle())
                    } else {
                        Circle()
                            .fill(DesignSystem.Colors.mutedGold)
                            .frame(width: 40, height: 40)
                            .overlay(
                                Text(person.initials)
                                    .font(DesignSystem.Typography.callout)
                                    .fontWeight(.semibold)
                                    .foregroundColor(.white)
                            )
                    }
                }
                
                VStack(alignment: .leading, spacing: 2) {
                    Text(person.fullName)
                        .font(DesignSystem.Typography.callout)
                        .foregroundColor(DesignSystem.Colors.warmBlack)
                        .lineLimit(1)
                    
                    if let relationship = person.relationship, !relationship.isEmpty {
                        Text(relationship)
                            .font(DesignSystem.Typography.caption)
                            .foregroundColor(DesignSystem.Colors.subtleText)
                            .lineLimit(1)
                    }
                }
                
                Spacer()
            }
            .padding(DesignSystem.Spacing.sm)
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - Preview
#Preview {
    let context = PersistenceController.preview.container.viewContext
    let samplePerson = Person(context: context)
    samplePerson.firstName = "John"
    samplePerson.lastName = "Doe"
    samplePerson.email = "<EMAIL>"
    samplePerson.phoneNumber = "1234567890"
    samplePerson.relationship = "Friend"
    samplePerson.lastContactDate = Date()

    return ZStack {
        LinearGradient(
            colors: [
                DesignSystem.Colors.primaryCream,
                DesignSystem.Colors.secondaryCream
            ],
            startPoint: .topLeading,
            endPoint: .bottomTrailing
        )
        .ignoresSafeArea()

        VStack(spacing: DesignSystem.Spacing.lg) {
            PersonBubbleView(
                person: samplePerson,
                isSelected: false,
                onTap: {},
                onDelete: {}
            )

            PersonBubbleView(
                person: samplePerson,
                isSelected: true,
                onTap: {},
                onDelete: {}
            )

            PersonBubbleCompactView(person: samplePerson) {}
        }
        .padding(DesignSystem.Spacing.lg)
    }
}
