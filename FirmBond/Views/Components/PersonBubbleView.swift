//
//  PersonBubbleView.swift
//  FirmBond
//
//  Created by <PERSON><PERSON> on 02/06/2025.
//

import SwiftUI

struct PersonBubbleView: View {
    
    // MARK: - Properties
    let person: Person
    let isSelected: Bool
    let onTap: () -> Void
    let onDelete: (() -> Void)?
    
    @State private var showingDeleteConfirmation = false
    @State private var dragOffset = CGSize.zero
    @State private var isShowingActions = false
    
    // MARK: - Initialization
    init(
        person: Person,
        isSelected: Bool = false,
        onTap: @escaping () -> Void,
        onDelete: (() -> Void)? = nil
    ) {
        self.person = person
        self.isSelected = isSelected
        self.onTap = onTap
        self.onDelete = onDelete
    }
    
    // MARK: - Body
    var body: some View {
        ContactCard(isSelected: isSelected, onTap: onTap) {
            HStack(spacing: DesignSystem.Spacing.md) {
                // Profile image or initials
                profileImageView
                
                // Contact information
                contactInfoView
                
                Spacer()
                
                // Status indicators
                statusIndicators
            }
        }
        .offset(x: dragOffset.width)
        .background(deleteBackground)
        .gesture(swipeGesture)
        .confirmationDialog(
            "Delete Contact",
            isPresented: $showingDeleteConfirmation,
            titleVisibility: .visible
        ) {
            Button("Delete", role: .destructive) {
                withAnimation(DesignSystem.Animations.spring) {
                    onDelete?()
                }
            }
            Button("Cancel", role: .cancel) {}
        } message: {
            Text("Are you sure you want to delete \(person.fullName)?")
        }
    }
    
    // MARK: - Private Views
    private var profileImageView: some View {
        Group {
            if let photoData = person.photoData,
               let uiImage = UIImage(data: photoData) {
                Image(uiImage: uiImage)
                    .resizable()
                    .aspectRatio(contentMode: .fill)
                    .frame(width: 56, height: 56)
                    .clipShape(Circle())
            } else {
                Circle()
                    .fill(
                        LinearGradient(
                            colors: [
                                DesignSystem.Colors.mutedGold,
                                DesignSystem.Colors.sageGreen
                            ],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
                    .frame(width: 56, height: 56)
                    .overlay(
                        Text(person.initials)
                            .font(DesignSystem.Typography.title3)
                            .fontWeight(.semibold)
                            .foregroundColor(.white)
                    )
            }
        }
        .shadow(
            color: DesignSystem.Colors.glassShadow,
            radius: 4,
            x: 0,
            y: 2
        )
    }
    
    private var contactInfoView: some View {
        VStack(alignment: .leading, spacing: 4) {
            // Name
            Text(person.fullName)
                .font(DesignSystem.Typography.title3)
                .foregroundColor(DesignSystem.Colors.warmBlack)
                .lineLimit(1)
            
            // Relationship or email
            if let relationship = person.relationship, !relationship.isEmpty {
                Text(relationship)
                    .font(DesignSystem.Typography.callout)
                    .foregroundColor(DesignSystem.Colors.subtleText)
                    .lineLimit(1)
            } else if let email = person.email, !email.isEmpty {
                Text(email)
                    .font(DesignSystem.Typography.monoCallout)
                    .foregroundColor(DesignSystem.Colors.subtleText)
                    .lineLimit(1)
            }
            
            // Phone number
            if !person.displayPhoneNumber.isEmpty {
                Text(person.displayPhoneNumber)
                    .font(DesignSystem.Typography.monoFootnote)
                    .foregroundColor(DesignSystem.Colors.subtleText)
                    .lineLimit(1)
            }
        }
    }
    
    private var statusIndicators: some View {
        VStack(alignment: .trailing, spacing: 4) {
            // Recent contact indicator
            if let lastContact = person.lastContactDate {
                let daysSince = Calendar.current.dateComponents([.day], from: lastContact, to: Date()).day ?? 0
                
                if daysSince <= 7 {
                    Circle()
                        .fill(DesignSystem.Colors.sageGreen)
                        .frame(width: 8, height: 8)
                }
            }
            
            // Contact methods available
            HStack(spacing: 4) {
                if !(person.phoneNumber?.isEmpty ?? true) {
                    Image(systemName: "phone.fill")
                        .font(.caption2)
                        .foregroundColor(DesignSystem.Colors.subtleText)
                }
                
                if !(person.email?.isEmpty ?? true) {
                    Image(systemName: "envelope.fill")
                        .font(.caption2)
                        .foregroundColor(DesignSystem.Colors.subtleText)
                }
            }
        }
    }
    
    private var deleteBackground: some View {
        HStack {
            Spacer()
            
            if abs(dragOffset.width) > 50 {
                RoundedRectangle(cornerRadius: DesignSystem.CornerRadius.large)
                    .fill(Color.red.opacity(0.8))
                    .frame(width: 80)
                    .overlay(
                        Image(systemName: "trash")
                            .font(.title2)
                            .foregroundColor(.white)
                    )
                    .transition(.scale.combined(with: .opacity))
            }
        }
    }
    
    // MARK: - Gestures
    private var swipeGesture: some Gesture {
        DragGesture()
            .onChanged { value in
                // Only allow left swipe for delete
                if value.translation.width < 0 {
                    dragOffset = value.translation
                }
            }
            .onEnded { value in
                withAnimation(DesignSystem.Animations.spring) {
                    if value.translation.width < -100 && onDelete != nil {
                        // Show delete confirmation
                        showingDeleteConfirmation = true
                    }
                    dragOffset = .zero
                }
            }
    }
}

// MARK: - Compact Variant
struct PersonBubbleCompactView: View {
    let person: Person
    let onTap: () -> Void
    
    var body: some View {
        Button(action: onTap) {
            HStack(spacing: DesignSystem.Spacing.sm) {
                // Smaller profile image
                Group {
                    if let photoData = person.photoData,
                       let uiImage = UIImage(data: photoData) {
                        Image(uiImage: uiImage)
                            .resizable()
                            .aspectRatio(contentMode: .fill)
                            .frame(width: 40, height: 40)
                            .clipShape(Circle())
                    } else {
                        Circle()
                            .fill(DesignSystem.Colors.mutedGold)
                            .frame(width: 40, height: 40)
                            .overlay(
                                Text(person.initials)
                                    .font(DesignSystem.Typography.callout)
                                    .fontWeight(.semibold)
                                    .foregroundColor(.white)
                            )
                    }
                }
                
                VStack(alignment: .leading, spacing: 2) {
                    Text(person.fullName)
                        .font(DesignSystem.Typography.callout)
                        .foregroundColor(DesignSystem.Colors.warmBlack)
                        .lineLimit(1)
                    
                    if let relationship = person.relationship, !relationship.isEmpty {
                        Text(relationship)
                            .font(DesignSystem.Typography.caption)
                            .foregroundColor(DesignSystem.Colors.subtleText)
                            .lineLimit(1)
                    }
                }
                
                Spacer()
            }
            .padding(DesignSystem.Spacing.sm)
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - Preview
#Preview {
    let context = PersistenceController.preview.container.viewContext
    let samplePerson = Person(context: context)
    samplePerson.firstName = "John"
    samplePerson.lastName = "Doe"
    samplePerson.email = "<EMAIL>"
    samplePerson.phoneNumber = "1234567890"
    samplePerson.relationship = "Friend"
    samplePerson.lastContactDate = Date()

    return ZStack {
        LinearGradient(
            colors: [
                DesignSystem.Colors.primaryCream,
                DesignSystem.Colors.secondaryCream
            ],
            startPoint: .topLeading,
            endPoint: .bottomTrailing
        )
        .ignoresSafeArea()

        VStack(spacing: DesignSystem.Spacing.lg) {
            PersonBubbleView(
                person: samplePerson,
                isSelected: false,
                onTap: {},
                onDelete: {}
            )

            PersonBubbleView(
                person: samplePerson,
                isSelected: true,
                onTap: {},
                onDelete: {}
            )

            PersonBubbleCompactView(person: samplePerson) {}
        }
        .padding(DesignSystem.Spacing.lg)
    }
}
